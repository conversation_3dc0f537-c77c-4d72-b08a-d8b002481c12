/// Member Profile Page - GymKod Pro Mobile
///
/// Bu sayfa üyelerin profil bilgilerini görüntülemesi ve çıkış yapması için oluşturulmuştur.
/// Angular frontend'deki tasarım sistemini takip eder.
///
/// RESPONSIVE DESIGN ENHANCEMENT:
/// Bu sayfa artık responsive tasarım desteği içerir.
/// Angular frontend'deki responsive profile pattern'leri uy<PERSON>tır.
///
/// RESPONSIVE FEATURES:
/// - Responsive dialog design with proper button styling
/// - Responsive typography scaling
/// - Responsive spacing ve padding
/// - Responsive icon sizes
/// - Responsive button heights ve border radius
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/core.dart';
import '../../../../core/providers/profile_image_provider.dart';
import '../../../../core/services/profile_image_service_v2.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import 'member_profile_edit_page.dart';

/// Member Profile Page
/// Member rolündeki kullanıcılar için profil sayfası
class MemberProfilePage extends ConsumerStatefulWidget {
  const MemberProfilePage({super.key});

  @override
  ConsumerState<MemberProfilePage> createState() => _MemberProfilePageState();
}

class _MemberProfilePageState extends ConsumerState<MemberProfilePage> {
  final ImagePicker _imagePicker = ImagePicker();
  bool _isUploadingImage = false;

  @override
  void initState() {
    super.initState();
    // Global provider'dan profil fotoğrafını yükle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileImageProvider.notifier).loadProfileImage();
      // Profil bilgilerini de yenile
      ref.read(authProvider.notifier).refreshProfile();
    });
  }





  /// Responsive profil fotoğrafı boyutu
  double _getProfileImageSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 600) {
      return 150; // Tablet/Desktop (120 → 150)
    } else if (screenWidth > 400) {
      return 130; // Büyük telefon (100 → 130)
    } else {
      return 115; // Küçük telefon (90 → 115)
    }
  }

  /// Responsive profil ikonu boyutu
  double _getProfileIconSize(BuildContext context) {
    return _getProfileImageSize(context) * 0.5;
  }

  /// Responsive kamera ikonu boyutu
  double _getCameraIconSize(BuildContext context) {
    final imageSize = _getProfileImageSize(context);
    return imageSize * 0.28; // Profil fotoğrafının %28'i (biraz daha küçük)
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = ref.watch(currentUserProvider);

    return Scaffold(
      // Body
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.primary.withValues(alpha: 0.8),
              theme.colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(
                maxWidth: 600,
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                  const SizedBox(height: 20),

                  // Profil Kartı
                  _buildProfileCard(theme, user),

                  const SizedBox(height: 20),

                  // Ayarlar Kartı
                  _buildSettingsCard(theme, context, ref),

                  const SizedBox(height: 20),

                  // Çıkış Kartı
                  _buildLogoutCard(theme, context, ref),

                  const SizedBox(height: 20),

                  // App Version
                  _buildVersionInfo(theme),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Profil Kartı - Responsive
  Widget _buildProfileCard(ThemeData theme, user) {
    return Card(
      elevation: AppSpacing.elevationMD,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.cardRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.cardPadding),
        child: Column(
          children: [
            // Profil Fotoğrafı - Responsive Size
            Consumer(
              builder: (context, ref, child) {
                final profileImageUrl = ref.watch(profileImageUrlProvider);

                return GestureDetector(
                  onTap: _showImagePickerDialog,
                  child: Stack(
                    children: [
                      Container(
                        width: _getProfileImageSize(context),
                        height: _getProfileImageSize(context),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: profileImageUrl != null ? null : LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              theme.colorScheme.primary,
                              theme.colorScheme.primary.withValues(alpha: 0.8),
                            ],
                          ),
                        ),
                        child: profileImageUrl != null
                            ? ClipOval(
                                child: CachedNetworkImage(
                                  imageUrl: profileImageUrl,
                                  width: _getProfileImageSize(context),
                                  height: _getProfileImageSize(context),
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => Center(
                                    child: CircularProgressIndicator(
                                      strokeWidth: 3,
                                      color: theme.colorScheme.primary,
                                    ),
                                  ),
                                  errorWidget: (context, url, error) => Icon(
                                    Icons.person,
                                    size: _getProfileIconSize(context),
                                    color: theme.colorScheme.onPrimary,
                                  ),
                                ),
                              )
                        : Icon(
                            Icons.person,
                            size: _getProfileIconSize(context),
                            color: theme.colorScheme.onPrimary,
                          ),
                      ),
                      // Yükleme göstergesi
                      if (_isUploadingImage)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.black.withValues(alpha: 0.5),
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 3,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      // Kamera ikonu - Responsive
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: _getCameraIconSize(context),
                          height: _getCameraIconSize(context),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: theme.colorScheme.primary,
                            border: Border.all(
                              color: theme.colorScheme.surface,
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            Icons.camera_alt,
                            size: _getCameraIconSize(context) * 0.5,
                            color: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // Kullanıcı Bilgileri
            if (user != null) ...[
              Text(
                user.name,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),


            ],
          ],
        ),
      ),
    );
  }

  /// Ayarlar Kartı - Responsive
  Widget _buildSettingsCard(ThemeData theme, BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeProvider);
    final themeService = ref.watch(themeServiceProvider);
    final systemBrightness = MediaQuery.of(context).platformBrightness;
    final user = ref.watch(currentUserProvider);

    return Card(
      elevation: AppSpacing.elevationMD,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.cardRadius),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Icon(
              themeService.getThemeModeIcon(themeMode, systemBrightness),
              color: theme.colorScheme.primary,
            ),
            title: const Text('Tema'),
            subtitle: Text(themeService.getThemeModeDescription(themeMode, systemBrightness)),
            trailing: Switch(
              value: themeService.isDarkMode(themeMode, systemBrightness),
              onChanged: (value) => _toggleTheme(ref, themeService, systemBrightness),
            ),
            onTap: () => _toggleTheme(ref, themeService, systemBrightness),
          ),
          const Divider(height: 1),
          // Profil Düzenleme (sadece member rolü için)
          if (user?.role == 'member') ...[
            ListTile(
              leading: Icon(
                Icons.edit,
                color: theme.colorScheme.primary,
              ),
              title: const Text('Profilimi Düzenle'),
              subtitle: const Text('Kişisel bilgilerinizi güncelleyin'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _navigateToProfileEdit(context),
            ),
            const Divider(height: 1),
          ],
          ListTile(
            leading: Icon(
              Icons.lock,
              color: theme.colorScheme.primary,
            ),
            title: const Text('Şifre Değiştir'),
            subtitle: const Text('Hesap şifrenizi değiştirin'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // Şifre değiştirme sayfasına git
              context.push(AppRoutes.changePassword);
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(
              Icons.notifications,
              color: theme.colorScheme.primary,
            ),
            title: const Text('Bildirimler'),
            subtitle: const Text('Bildirim ayarları'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // TODO: Bildirim ayarları sayfasına git
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Bildirim ayarları yakında eklenecek')),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Çıkış Kartı - Responsive
  Widget _buildLogoutCard(ThemeData theme, BuildContext context, WidgetRef ref) {
    return Card(
      elevation: AppSpacing.elevationMD,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.cardRadius),
      ),
      child: ListTile(
        leading: Icon(
          Icons.logout,
          color: theme.colorScheme.error,
        ),
        title: Text(
          'Çıkış Yap',
          style: TextStyle(
            color: theme.colorScheme.error,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: const Text('Hesabınızdan güvenli çıkış yapın'),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: theme.colorScheme.error,
        ),
        onTap: () => _showLogoutDialog(context, theme, ref),
      ),
    );
  }

  /// Version Bilgisi
  Widget _buildVersionInfo(ThemeData theme) {
    return Text(
      'GymKod Pro v${AppConstants.appVersion}',
      style: theme.textTheme.bodySmall?.copyWith(
        color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      ),
    );
  }

  /// Çıkış onay dialogu göster
  void _showLogoutDialog(BuildContext context, ThemeData theme, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          title: Row(
            children: [
              Icon(
                Icons.logout,
                color: theme.colorScheme.error,
                size: 24.0,
              ),
              const SizedBox(width: 8.0),
              const Text(
                'Çıkış Yap',
                style: TextStyle(
                  fontSize: 20.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          content: const Text(
            'Hesabınızdan çıkış yapmak istediğinizden emin misiniz?',
            style: TextStyle(fontSize: 16.0),
          ),
          actionsPadding: const EdgeInsets.all(16.0),
          actions: [
            // Butonları Column ile dikey hizala
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // İptal Butonu (üstte)
                OutlinedButton(
                  onPressed: () => Navigator.of(dialogContext).pop(),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(
                      color: theme.colorScheme.outline,
                      width: 1.5,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20.0,
                      vertical: 12.0,
                    ),
                  ),
                  child: Text(
                    'İptal',
                    style: TextStyle(
                      fontSize: 14.0,
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),

                const SizedBox(height: 8.0),

                // Çıkış Yap Butonu (altta)
                ElevatedButton(
                  onPressed: () async {
                    // Dialog'u kapat
                    Navigator.of(dialogContext).pop();

                    // Çıkış işlemini gerçekleştir
                    await _performLogout(context, ref);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.error,
                    foregroundColor: theme.colorScheme.onError,
                    elevation: 3.0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20.0,
                      vertical: 12.0,
                    ),
                  ),
                  child: const Text(
                    'Çıkış Yap',
                    style: TextStyle(
                      fontSize: 14.0,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  /// Çıkış işlemini gerçekleştir
  Future<void> _performLogout(BuildContext context, WidgetRef ref) async {
    LoggingService.info('User logout requested from member profile page', tag: 'MEMBER_PROFILE');

    // Auth provider'dan logout
    await ref.read(authProvider.notifier).logout();

    // Login sayfasına yönlendir
    if (context.mounted) {
      context.go(AppRoutes.login);
    }
  }

  /// Profil fotoğrafı seçme dialogunu göster
  void _showImagePickerDialog() {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Başlık
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: theme.colorScheme.outline,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Profil Fotoğrafı',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              // Seçenekler
              Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // Kamera
                      _buildImagePickerOption(
                        icon: Icons.camera_alt,
                        label: 'Kamera',
                        onTap: () {
                          Navigator.pop(context);
                          _pickImageFromCamera();
                        },
                        theme: theme,
                      ),

                      // Galeri
                      _buildImagePickerOption(
                        icon: Icons.photo_library,
                        label: 'Galeri',
                        onTap: () {
                          Navigator.pop(context);
                          _pickImageFromGallery();
                        },
                        theme: theme,
                      ),

                      // Sil (eğer profil fotoğrafı varsa)
                      Consumer(
                        builder: (context, ref, child) {
                          final profileImageUrl = ref.watch(profileImageUrlProvider);
                          if (profileImageUrl != null) {
                            return _buildImagePickerOption(
                              icon: Icons.delete,
                              label: 'Sil',
                              onTap: () {
                                Navigator.pop(context);
                                _deleteProfileImage();
                              },
                              theme: theme,
                              isDestructive: true,
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),
                ],
              ),

              const SizedBox(height: 24),
            ],
          ),
        );
      },
    );
  }

  /// Image picker seçeneği widget'ı
  Widget _buildImagePickerOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required ThemeData theme,
    bool isDestructive = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDestructive
                  ? theme.colorScheme.errorContainer
                  : theme.colorScheme.primaryContainer,
            ),
            child: Icon(
              icon,
              size: 30,
              color: isDestructive
                  ? theme.colorScheme.onErrorContainer
                  : theme.colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDestructive
                  ? theme.colorScheme.error
                  : theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  /// Kameradan fotoğraf seç - Production Optimized
  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
        preferredCameraDevice: CameraDevice.front,
      );

      if (image != null && mounted) {
        await _uploadProfileImageFromXFile(image);
      }
    } catch (e) {
      LoggingService.error('Camera error', error: e, tag: 'CAMERA');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Kamera hatası: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Galeriden fotoğraf seç
  Future<void> _pickImageFromGallery() async {
    try {
      LoggingService.info('Starting gallery image picker', tag: 'PROFILE_IMAGE_DEBUG');

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        LoggingService.info('Image selected from gallery: ${image.path}', tag: 'PROFILE_IMAGE_DEBUG');
        await _uploadProfileImageFromXFile(image);
      } else {
        LoggingService.info('No image selected from gallery', tag: 'PROFILE_IMAGE_DEBUG');
      }
    } catch (e, stackTrace) {
      LoggingService.error('Gallery image pick error', error: e, stackTrace: stackTrace, tag: 'PROFILE_IMAGE_DEBUG');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Galeri erişiminde hata oluştu: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Profil fotoğrafını yükle - XFile için (Web uyumlu)
  Future<void> _uploadProfileImageFromXFile(XFile imageFile) async {
    if (!mounted) return;

    setState(() => _isUploadingImage = true);

    try {
      LoggingService.info('Starting profile image upload from XFile', tag: 'PROFILE_IMAGE_DEBUG');
      LoggingService.info('XFile path: ${imageFile.path}', tag: 'PROFILE_IMAGE_DEBUG');
      LoggingService.info('XFile name: ${imageFile.name}', tag: 'PROFILE_IMAGE_DEBUG');

      final profileImageService = ref.read(profileImageServiceV2Provider);
      LoggingService.info('Profile image service obtained', tag: 'PROFILE_IMAGE_DEBUG');

      final result = await profileImageService.uploadProfileImageFromXFile(imageFile);
      LoggingService.info('Upload result: Success=${result.isSuccess}, Message=${result.message}', tag: 'PROFILE_IMAGE_DEBUG');

      if (!mounted) return;

      if (result.isSuccess) {
        // Global provider ile cache bypass
        ref.read(profileImageProvider.notifier).loadProfileImageWithCacheBuster();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
        }
      } else {
        LoggingService.error('Upload failed: ${result.message}', tag: 'PROFILE_IMAGE_DEBUG');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.message),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e, stackTrace) {
      LoggingService.error('Upload error', error: e, stackTrace: stackTrace, tag: 'PROFILE_IMAGE_DEBUG');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Yükleme hatası: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isUploadingImage = false);
      }
    }
  }



  /// Profil fotoğrafını sil - Production Optimized
  Future<void> _deleteProfileImage() async {
    try {
      final profileImageService = ref.read(profileImageServiceV2Provider);
      final result = await profileImageService.deleteProfileImage();

      if (result.isSuccess) {
        // Cache'i agresif şekilde temizle
        final user = ref.read(currentUserProvider);
        if (user != null) {
          final userId = int.tryParse(user.nameidentifier);
          if (userId != null) {
            final profileImageService = ref.read(profileImageServiceV2Provider);
            final baseUrl = profileImageService.getProfileImageUrl(userId);
            // Tüm cache varyasyonlarını temizle
            await CachedNetworkImage.evictFromCache(baseUrl);
            await CachedNetworkImage.evictFromCache('$baseUrl?t=${DateTime.now().millisecondsSinceEpoch}');
          }
        }

        // Global provider'dan profil fotoğrafını temizle
        ref.read(profileImageProvider.notifier).clearProfileImage();

        // Provider'ı force refresh et
        await Future.delayed(const Duration(milliseconds: 100));
        ref.read(profileImageProvider.notifier).loadProfileImage();

        // Profil sayfasını yenile (UI güncellemesi için)
        if (mounted) {
          setState(() {});
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    } catch (e) {
      LoggingService.error('Delete error', error: e, tag: 'PROFILE_IMAGE');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Silme hatası: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Tema değiştirme işlemi - Tüm ListTile için ortak metod
  Future<void> _toggleTheme(WidgetRef ref, ThemeService themeService, Brightness systemBrightness) async {
    // Tema değiştirme işlemi (Angular'daki toggleDarkMode'a benzer)
    await ref.read(themeProvider.notifier).toggleTheme(systemBrightness);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Tema değiştirildi: ${themeService.getThemeModeDescription(
              ref.read(themeProvider),
              systemBrightness
            )}'
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }



  /// Profil düzenleme sayfasına git
  void _navigateToProfileEdit(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const MemberProfileEditPage(),
      ),
    ).then((result) async {
      // Profil güncellendiyse kullanıcı bilgilerini yenile
      if (result == true) {
        // Auth provider'dan kullanıcı bilgilerini yenile
        await ref.read(authProvider.notifier).refreshProfile();

        // Profil sayfasını da yenile (setState ile)
        if (mounted) {
          setState(() {});
        }
      }
    });
  }
}
